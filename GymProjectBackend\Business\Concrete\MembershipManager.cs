﻿using Business.Abstract;
using Business.BusinessAscpects.Autofac;
using Business.Constants;
using Core.Aspects.Autofac.Caching;
using Core.Aspects.Autofac.Logging;
using Core.Aspects.Autofac.Performance;
using Core.Aspects.Autofac.Transaction;
using Core.Utilities.Results;
using Core.Utilities.Security.CompanyContext;
using DataAccess.Abstract;
using Entities.Concrete;
using Entities.DTOs;
using System;
using System.Linq;
using System.Transactions;

namespace Business.Concrete
{
    public class MembershipManager : IMembershipService
    {
        IMembershipDal _membershipDal;
        IPaymentDal _paymentDal;
        IRemainingDebtDal _remainingDebtDal;
        IMembershipFreezeHistoryService _freezeHistoryService;
        private readonly ICompanyContext _companyContext;

        public MembershipManager(IMembershipDal membershipDal,IPaymentDal paymentDal,IRemainingDebtDal remainingDebtDal, IMembershipFreezeHistoryService freezeHistoryService, ICompanyContext companyContext)
        {
            _membershipDal = membershipDal;
            _paymentDal = paymentDal;
            _remainingDebtDal = remainingDebtDal;
            _freezeHistoryService = freezeHistoryService;
            _companyContext = companyContext;
        }

        [SecuredOperation("owner,admin")]
        [LogAspect]
        [TransactionScopeAspect]
        [PerformanceAspect(3)]
        [CacheRemoveAspect("Membership")]
        public IResult CancelFreeze(int membershipId)
        {
            if (!_membershipDal.IsMembershipFrozen(membershipId))
                return new ErrorResult("Üyelik dondurulmamış durumda.");

            var membership = _membershipDal.Get(m => m.MembershipID == membershipId);
            if (membership == null)
                return new ErrorResult("Üyelik bulunamadı.");

            // En son dondurma kaydını bul ve güncelle
            var freezeHistories = _freezeHistoryService.GetByMembershipId(membershipId).Data;
            var lastFreezeHistory = freezeHistories.OrderByDescending(x => x.CreationDate).FirstOrDefault();

            if (lastFreezeHistory != null)
            {
                var history = new MembershipFreezeHistory
                {
                    FreezeHistoryID = lastFreezeHistory.FreezeHistoryID,
                    MembershipID = membershipId,
                    CompanyID = membership.CompanyID, // CompanyID eklendi - Multi-tenant fix
                    StartDate = lastFreezeHistory.StartDate,
                    PlannedEndDate = lastFreezeHistory.PlannedEndDate,
                    ActualEndDate = DateTime.Now,
                    FreezeDays = lastFreezeHistory.FreezeDays,
                    UsedDays = 0, // Tamamen iptal edildiği için kullanılan gün 0
                    CancellationType = "Tamamen İptal",
                    CreationDate = lastFreezeHistory.CreationDate
                };

                _freezeHistoryService.Update(history);
            }

            _membershipDal.CancelFreeze(membershipId);
            return new SuccessResult("Üyelik dondurma işlemi tamamen iptal edildi.");
        }

        [SecuredOperation("owner,admin")]
        [LogAspect]
        [PerformanceAspect(3)]
        [TransactionScopeAspect]
        [CacheRemoveAspect("Membership")]
        public IResult ReactivateFromToday(int membershipId)
        {
            if (!_membershipDal.IsMembershipFrozen(membershipId))
                return new ErrorResult("Üyelik dondurulmamış durumda.");

            var membership = _membershipDal.Get(m => m.MembershipID == membershipId);
            if (membership == null)
                return new ErrorResult("Üyelik bulunamadı.");

            // En son dondurma kaydını bul ve güncelle
            var freezeHistories = _freezeHistoryService.GetByMembershipId(membershipId).Data;
            var lastFreezeHistory = freezeHistories.OrderByDescending(x => x.CreationDate).FirstOrDefault();

            if (lastFreezeHistory != null)
            {
                var usedDays = (int)(DateTime.Now - lastFreezeHistory.StartDate).TotalDays;

                var history = new MembershipFreezeHistory
                {
                    FreezeHistoryID = lastFreezeHistory.FreezeHistoryID,
                    MembershipID = membershipId,
                    CompanyID = membership.CompanyID, // CompanyID eklendi - Multi-tenant fix
                    StartDate = lastFreezeHistory.StartDate,
                    PlannedEndDate = lastFreezeHistory.PlannedEndDate,
                    ActualEndDate = DateTime.Now,
                    FreezeDays = lastFreezeHistory.FreezeDays,
                    UsedDays = usedDays,
                    CancellationType = "Erken Başlatma",
                    CreationDate = lastFreezeHistory.CreationDate
                };

                _freezeHistoryService.Update(history);
            }

            _membershipDal.ReactivateFromToday(membershipId);
            return new SuccessResult("Üyelik bugünden itibaren aktif edildi.");
        }

        [SecuredOperation("owner,admin")]
        [LogAspect]
        [TransactionScopeAspect]
        [PerformanceAspect(3)]
        [CacheRemoveAspect("Membership")]
        public IResult FreezeMembership(MembershipFreezeRequestDto freezeRequest)
        {
            // Yıllık dondurma hakkı sınırı kaldırıldı - Sınırsız dondurma
            // Remaining days kontrolü artık yapılmıyor
            return _membershipDal.FreezeMembershipWithValidation(freezeRequest, int.MaxValue);
        }

        [SecuredOperation("owner,admin")]
        [LogAspect]
        [PerformanceAspect(3)]
        [TransactionScopeAspect]
        [CacheRemoveAspect("Membership")]
        public IResult UnfreezeMembership(int membershipId)
        {
            if (!_membershipDal.IsMembershipFrozen(membershipId))
                return new ErrorResult(Messages.MembershipAlreadyFrozen);

            _membershipDal.UnfreezeMembership(membershipId);
            return new SuccessResult(Messages.MembershipUnfrozen);
        }

        [SecuredOperation("owner,admin")]
        [PerformanceAspect(3)]
        public IDataResult<List<MembershipFreezeDto>> GetFrozenMemberships()
        {
            return new SuccessDataResult<List<MembershipFreezeDto>>(_membershipDal.GetFrozenMemberships());
        }
        [SecuredOperation("owner,admin")]
        [LogAspect]
        [PerformanceAspect(3)]
        [CacheRemoveAspect("Membership")]
        public IResult Add(MembershipAddDto membershipDto)
        {
            // SOLID prensiplerine uygun: Karmaşık ekleme işlemini DAL katmanına taşıdık
            return _membershipDal.AddMembershipWithPaymentAndDebt(membershipDto);
        }
        [SecuredOperation("owner,admin")]
        [LogAspect]
        [PerformanceAspect(3)]
        [CacheRemoveAspect("Membership")]
        public IResult Update(MembershipUpdateDto membershipDto)
        {
            // SOLID prensiplerine uygun: Entity manipülasyon ve tarih yönetimini DAL katmanına taşıdık
            return _membershipDal.UpdateMembershipWithDateManagement(membershipDto, _companyContext.GetCompanyId());
        }
        [SecuredOperation("owner,admin")]
        [LogAspect]
        [PerformanceAspect(3)]
        [CacheRemoveAspect("Membership")]
        public IResult Delete(int id)
        {
            // SOLID prensiplerine uygun: Karmaşık silme işlemini DAL katmanına taşıdık
            return _membershipDal.DeleteMembershipWithRelatedData(id, _companyContext.GetCompanyId());
        }
        [SecuredOperation("owner,admin")]
        [PerformanceAspect(3)]
        [CacheAspect(15)] // 15 dakika cache (tüm üyelikler)
        public IDataResult<List<Membership>> GetAll()
        {
            return new SuccessDataResult<List<Membership>>(_membershipDal.GetAll());
        }
        [SecuredOperation("owner,admin")]
        [CacheAspect(15)] // 15 dakika cache (ID'ye göre üyelik)
        public IDataResult<List<Membership>> GetByMembershipId(int memberid)
        {
            return new SuccessDataResult<List<Membership>>(_membershipDal.GetAll(c => c.MembershipID == memberid));
        }
        [SecuredOperation("owner,admin")]
        [PerformanceAspect(3)]
        public IDataResult<LastMembershipInfoDto> GetLastMembershipInfo(int memberId)
        {
            // SOLID prensiplerine uygun: LINQ operations ve complex calculations DAL katmanına taşıdık
            return _membershipDal.GetLastMembershipInfoWithCalculations(memberId, _companyContext.GetCompanyId());
        }

        [SecuredOperation("owner,admin")]
        [PerformanceAspect(3)]
        public IDataResult<List<MembershipDetailForDeleteDto>> GetMemberActiveMemberships(int memberId)
        {
            return new SuccessDataResult<List<MembershipDetailForDeleteDto>>(_membershipDal.GetMemberActiveMemberships(memberId));
        }
    }
}
