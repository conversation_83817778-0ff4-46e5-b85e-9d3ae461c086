using Business.Abstract;
using Business.BusinessAscpects.Autofac;
using Business.Constants;
using Core.Aspects.Autofac.Caching;
using Core.Aspects.Autofac.Logging;
using Core.Aspects.Autofac.Performance;
using Core.Utilities.Results;
using DataAccess.Abstract;
using Entities.Concrete;
using Entities.DTOs;
using System.Collections.Generic;

namespace Business.Concrete
{
    public class MembershipFreezeHistoryManager : IMembershipFreezeHistoryService
    {
        private readonly IMembershipFreezeHistoryDal _membershipFreezeHistoryDal;

        public MembershipFreezeHistoryManager(IMembershipFreezeHistoryDal membershipFreezeHistoryDal)
        {
            _membershipFreezeHistoryDal = membershipFreezeHistoryDal;
        }

        [SecuredOperation("owner,admin")]
        [PerformanceAspect(3)]
        [CacheAspect(15)] // 15 dakika cache (dondurma geçmişi)
        public IDataResult<List<MembershipFreezeHistoryDto>> GetAll()
        {
            var result = _membershipFreezeHistoryDal.GetFreezeHistoryDetails();
            return new SuccessDataResult<List<MembershipFreezeHistoryDto>>(result);
        }

        [SecuredOperation("owner,admin")]
        [PerformanceAspect(3)]
        [CacheAspect(15)] // 15 dakika cache (üyelik geçmişi)
        public IDataResult<List<MembershipFreezeHistoryDto>> GetByMembershipId(int membershipId)
        {
            var result = _membershipFreezeHistoryDal.GetFreezeHistoryByMembershipId(membershipId);
            return new SuccessDataResult<List<MembershipFreezeHistoryDto>>(result);
        }

        [SecuredOperation("owner,admin")]
        [LogAspect]
        [CacheRemoveAspect("MembershipFreezeHistory")]
        public IResult Add(MembershipFreezeHistory history)
        {
            _membershipFreezeHistoryDal.Add(history);
            return new SuccessResult();
        }

        [SecuredOperation("owner,admin")]
        [LogAspect]
        [CacheRemoveAspect("MembershipFreezeHistory")]
        public IResult Update(MembershipFreezeHistory history)
        {
            _membershipFreezeHistoryDal.Update(history);
            return new SuccessResult();
        }

        [SecuredOperation("owner,admin")]
        [PerformanceAspect(3)]
        [CacheAspect(30)] // 30 dakika cache - kalan dondurma günü hesaplama
        public IDataResult<int> GetRemainingFreezeDays(int membershipId)
        {
            // SOLID prensiplerine uygun: Karmaşık hesaplama işlemini DAL katmanına taşıdık
            var remainingDays = _membershipFreezeHistoryDal.GetRemainingFreezeDaysWithCalculation(membershipId);
            return new SuccessDataResult<int>(remainingDays);
        }
    }
}