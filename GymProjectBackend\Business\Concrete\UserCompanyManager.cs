using Business.Abstract;
using Business.BusinessAscpects.Autofac;
using Business.Constants;
using Business.ValidationRules.FluentValidation;
using Core.Aspects.Autofac.Caching;
using Core.Aspects.Autofac.Logging;
using Core.Aspects.Autofac.Performance;
using Core.Aspects.Autofac.Validation;
using Core.Utilities.Results;
using DataAccess.Abstract;
using DataAccess.Concrete.EntityFramework;
using Entities.Concrete;
using Entities.DTOs;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Business.Concrete
{
    public class UserCompanyManager : IUserCompanyService
    {
        IUserCompanyDal _userCompanyDal;

        public UserCompanyManager(IUserCompanyDal userCompanyDal)
        {
            _userCompanyDal = userCompanyDal;
        }
        [SecuredOperation("owner")]
        [LogAspect]
        [PerformanceAspect(3)]
        [ValidationAspect(typeof(UserCompanyValidator))]
        [CacheRemoveAspect("UserCompany")]
        public IResult Add(UserCompany userCompany)
        {
            // SOLID prensiplerine uygun: Business logic DAL katmanına taşındı
            return _userCompanyDal.AddUserCompanyWithValidation(userCompany);
        }
        [SecuredOperation("owner")]
        [LogAspect]
        [CacheRemoveAspect("UserCompany")]
        public IResult Delete(int id)
        {
            _userCompanyDal.Delete(id);
            return new SuccessResult(Messages.UserCompanyDeleted);
        }
        [SecuredOperation("owner")]
        [CacheAspect(120)] // 120 dakika cache - kullanıcı-şirket ilişkileri az değişir
        public IDataResult<List<UserCompany>> GetAll()
        {
            return new SuccessDataResult<List<UserCompany>>(_userCompanyDal.GetAll());
        }
        [SecuredOperation("owner")]
        [LogAspect]
        [PerformanceAspect(3)]
        [ValidationAspect(typeof(UserCompanyValidator))]
        [CacheRemoveAspect("UserCompany")]
        public IResult Update(UserCompany userCompany)
        {
            _userCompanyDal.Update(userCompany);
            return new SuccessResult(Messages.UserCompanyUpdated);
        }
        [SecuredOperation("owner")]
        [PerformanceAspect(3)]
        [CacheAspect(90)] // 90 dakika cache - detaylı kullanıcı-şirket bilgileri az değişir
        public IDataResult<List<UserCompanyDetailDto>> GetUserCompanyDetails()
        {
            return new SuccessDataResult<List<UserCompanyDetailDto>>(_userCompanyDal.GetUserCompanyDetails());
        }

        [PerformanceAspect(3)]
        [CacheAspect(180)] // 180 dakika cache - kullanıcı şirket ID'si çok az değişir
        public IDataResult<int> GetUserCompanyId(int userId)
        {
            int companyId = _userCompanyDal.GetUserCompanyId(userId);
            return new SuccessDataResult<int>(companyId);
        }

        [PerformanceAspect(3)]
        [CacheAspect(120)] // 120 dakika cache - kullanıcı şirketleri az değişir
        public IDataResult<List<UserCompany>> GetUserCompanies(int userId)
        {
            var userCompanies = _userCompanyDal.GetActiveUserCompanies(userId);
            if (userCompanies == null || userCompanies.Count == 0)
            {
                return new ErrorDataResult<List<UserCompany>>(Messages.UserCompanyNotFound);
            }
            return new SuccessDataResult<List<UserCompany>>(userCompanies);
        }

        [LogAspect]
        [PerformanceAspect(3)]
        [CacheRemoveAspect("UserCompany")]
        public IResult UpdateActiveCompany(int userId, int companyId)
        {
            // SOLID prensiplerine uygun: Business logic DAL katmanına taşındı
            return _userCompanyDal.UpdateActiveCompanyWithValidation(userId, companyId);
        }
    }
}
